
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>可见性检测修复验证</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 20px; 
                background-color: #f0f8ff;
            }
            .container {
                max-width: 1000px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 20px;
                text-align: center;
            }
            .test-panel {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                border-left: 4px solid #007bff;
            }
            .btn {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                margin: 5px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                transition: background-color 0.3s;
            }
            .btn:hover { background-color: #0056b3; }
            .btn.danger { background-color: #dc3545; }
            .btn.danger:hover { background-color: #c82333; }
            .btn.success { background-color: #28a745; }
            .btn.success:hover { background-color: #218838; }
            
            #test-status {
                background: #e9ecef;
                padding: 15px;
                border-radius: 8px;
                margin-top: 15px;
                font-family: monospace;
                border-left: 4px solid #6c757d;
            }
            .count { 
                font-weight: bold; 
                color: #007bff; 
                font-size: 18px;
            }
            .names { 
                font-weight: bold; 
                color: #28a745; 
            }
            .mean-status { 
                font-weight: bold; 
                color: #dc3545; 
            }
            .instructions {
                background: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                border-left: 4px solid #ffc107;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔧 可见性检测修复验证</h1>
                <p>验证动态均值图表中线条可见性检测逻辑的修复效果</p>
            </div>
            
            <div class="instructions">
                <h3>📋 测试说明</h3>
                <ul>
                    <li><strong>问题描述:</strong> 隐藏线条后，均值线名称中的线条数量仍然不正确</li>
                    <li><strong>修复内容:</strong> 改进可见性检测逻辑，正确识别 visible 状态</li>
                    <li><strong>验证方法:</strong> 点击图例或使用测试按钮，观察控制台日志和状态显示</li>
                    <li><strong>预期结果:</strong> 均值线名称中的线条数量应与实际可见线条数量一致</li>
                </ul>
            </div>
            
            <div class="test-panel">
                <h3>🧪 自动化测试</h3>
                <button class="btn danger" onclick="testHideLineC()">隐藏Line C (应显示2条线的均值)</button>
                <button class="btn danger" onclick="testShowOnlyLineA()">只显示Line A (应隐藏均值线)</button>
                <button class="btn success" onclick="testShowAll()">显示所有线 (应显示3条线的均值)</button>
                
                <div id="test-status">等待测试...</div>
            </div>
            
            <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-2.35.2.min.js"></script>                <div id="3f14cf76-9b85-45b1-80cb-e14269fcfcf2" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("3f14cf76-9b85-45b1-80cb-e14269fcfcf2")) {                    Plotly.newPlot(                        "3f14cf76-9b85-45b1-80cb-e14269fcfcf2",                        [{"line":{"color":"blue","width":3},"marker":{"size":8},"mode":"lines+markers","name":"Line A","x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00"],"y":[1,2,3,4,5,4,3,2,1,0],"type":"scatter"},{"line":{"color":"green","width":3},"marker":{"size":8},"mode":"lines+markers","name":"Line B","x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00"],"y":[0,1,2,3,4,5,4,3,2,1],"type":"scatter"},{"line":{"color":"orange","width":3},"marker":{"size":8},"mode":"lines+markers","name":"Line C","x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00"],"y":[2,1,0,1,2,3,2,1,0,1],"type":"scatter"},{"line":{"color":"red","dash":"dash","width":4},"mode":"lines","name":"Selected Mean","visible":false,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00"],"y":[0,0,0,0,0,0,0,0,0,0],"type":"scatter"}],                        {"template":{"data":{"barpolar":[{"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"white","showlakes":true,"showland":true,"subunitcolor":"#C8D4E3"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"white","polar":{"angularaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""},"bgcolor":"white","radialaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"yaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"zaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"baxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"bgcolor":"white","caxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2}}},"title":{"text":"可见性检测修复验证"},"xaxis":{"title":{"text":"日期"}},"yaxis":{"title":{"text":"数值"}},"legend":{"title":{"text":"图例（点击测试）"}},"height":500},                        {"responsive": true}                    )                };                            </script>        </div>
            
    <script>
        const data = {'Line A': [1, 2, 3, 4, 5, 4, 3, 2, 1, 0], 'Line B': [0, 1, 2, 3, 4, 5, 4, 3, 2, 1], 'Line C': [2, 1, 0, 1, 2, 3, 2, 1, 0, 1]};
        const dates = ['2024-01-01 00:00:00', '2024-01-02 00:00:00', '2024-01-03 00:00:00', '2024-01-04 00:00:00', '2024-01-05 00:00:00', '2024-01-06 00:00:00', '2024-01-07 00:00:00', '2024-01-08 00:00:00', '2024-01-09 00:00:00', '2024-01-10 00:00:00'];
        const seriesNames = ['Line A', 'Line B', 'Line C'];
        
        let plotDiv;
        
        document.addEventListener('DOMContentLoaded', function() {
            plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
            if (!plotDiv) return;
            
            // 监听图例点击
            plotDiv.on('plotly_legendclick', function(eventData) {
                console.log('=== 图例点击事件 ===');
                setTimeout(updateMean, 100);
                return true;
            });
            
            // 监听图例双击
            plotDiv.on('plotly_legenddoubleclick', function(eventData) {
                console.log('=== 图例双击事件 ===');
                setTimeout(updateMean, 100);
                return true;
            });
            
            function updateMean() {
                console.log('\n--- 开始更新均值线 ---');
                const traces = plotDiv.data;
                const meanTraceIndex = traces.length - 1;
                
                console.log('总trace数量:', traces.length, '均值线索引:', meanTraceIndex);
                
                // 找出可见的数据线（修复后的逻辑）
                const visibleIndices = [];
                for (let i = 0; i < traces.length - 1; i++) {
                    const visibleState = traces[i].visible;
                    const isVisible = visibleState === true || visibleState === undefined;
                    
                    console.log(`线条 ${i} (${seriesNames[i]}):`, {
                        'visible属性': visibleState,
                        '是否可见': isVisible,
                        '类型': typeof visibleState
                    });
                    
                    if (isVisible) {
                        visibleIndices.push(i);
                    }
                }
                
                console.log('\n可见线条统计:');
                console.log('- 可见索引:', visibleIndices);
                console.log('- 可见数量:', visibleIndices.length);
                console.log('- 可见名称:', visibleIndices.map(i => seriesNames[i]));
                
                // 更新状态显示
                updateTestStatus(visibleIndices);
                
                if (visibleIndices.length <= 1) {
                    console.log('\n🔴 隐藏均值线 - 原因: 可见线条数量 ≤ 1');
                    Plotly.restyle(plotDiv, {'visible': false}, [meanTraceIndex]);
                    return;
                }
                
                // 计算均值
                const meanValues = [];
                for (let i = 0; i < dates.length; i++) {
                    let sum = 0;
                    let count = 0;
                    
                    for (const idx of visibleIndices) {
                        sum += traces[idx].y[i];
                        count++;
                    }
                    
                    meanValues.push(count > 0 ? sum / count : 0);
                }
                
                // 更新均值线
                const selectedNames = visibleIndices.map(i => seriesNames[i]);
                const meanName = `均值 (${selectedNames.length}条线: ${selectedNames.join(', ')})`;
                
                console.log('\n🟢 显示均值线:');
                console.log('- 名称:', meanName);
                console.log('- 选中线条:', selectedNames);
                console.log('- 均值样本:', meanValues.slice(0, 3), '...');
                
                Plotly.restyle(plotDiv, {
                    'y': [meanValues],
                    'visible': true,
                    'name': meanName
                }, [meanTraceIndex]);
                
                console.log('--- 更新完成 ---\n');
            }
            
            // 初始化
            updateMean();
            
            // 暴露函数
            window.updateMean = updateMean;
            window.plotDiv = plotDiv;
        });
        
        function updateTestStatus(visibleIndices) {
            const statusDiv = document.getElementById('test-status');
            if (!statusDiv) return;
            
            const visibleNames = visibleIndices.map(i => seriesNames[i]);
            const timestamp = new Date().toLocaleTimeString();
            
            statusDiv.innerHTML = `
                <strong>当前状态 (${timestamp}):</strong><br>
                可见线条数量: <span class="count">${visibleIndices.length}</span><br>
                可见线条: <span class="names">${visibleNames.join(', ') || '无'}</span><br>
                均值线状态: <span class="mean-status">${visibleIndices.length > 1 ? '显示' : '隐藏'}</span>
            `;
        }
        
        // 测试函数
        function testHideLineC() {
            console.log('\n🧪 测试: 隐藏Line C');
            Plotly.restyle(plotDiv, {'visible': 'legendonly'}, [2]);
            setTimeout(updateMean, 100);
        }
        
        function testShowOnlyLineA() {
            console.log('\n🧪 测试: 只显示Line A');
            Plotly.restyle(plotDiv, {'visible': 'legendonly'}, [1, 2]);
            Plotly.restyle(plotDiv, {'visible': true}, [0]);
            setTimeout(updateMean, 100);
        }
        
        function testShowAll() {
            console.log('\n🧪 测试: 显示所有线');
            Plotly.restyle(plotDiv, {'visible': true}, [0, 1, 2]);
            setTimeout(updateMean, 100);
        }
        
        window.testHideLineC = testHideLineC;
        window.testShowOnlyLineA = testShowOnlyLineA;
        window.testShowAll = testShowAll;
    </script>
    
        </div>
    </body>
    </html>
    