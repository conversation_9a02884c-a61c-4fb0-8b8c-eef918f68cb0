"""
创建一个带有测试按钮的页面来验证动态均值图表的修复
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from datetime import datetime, timed<PERSON><PERSON>

def create_test_page():
    """创建带有测试按钮的页面"""
    
    # 创建示例数据
    dates = pd.date_range('2024-01-01', periods=30, freq='D')
    np.random.seed(42)
    
    # 生成3条不同的时间序列（减少数量便于测试）
    data = {
        'Line A': np.cumsum(np.random.randn(30) * 0.1),
        'Line B': np.cumsum(np.random.randn(30) * 0.15),
        'Line C': np.cumsum(np.random.randn(30) * 0.12)
    }
    
    df = pd.DataFrame(data, index=dates)
    
    # 创建图表
    fig = go.Figure()
    
    # 添加数据线
    colors = ['blue', 'green', 'orange']
    for i, col in enumerate(df.columns):
        fig.add_trace(go.Scatter(
            x=df.index,
            y=df[col],
            mode='lines',
            name=col,
            line=dict(color=colors[i], width=2),
            opacity=0.8
        ))
    
    # 添加均值线（初始隐藏）
    fig.add_trace(go.Scatter(
        x=df.index,
        y=[0] * len(df),
        mode='lines',
        name='Selected Mean',
        line=dict(color='red', width=3, dash='dash'),
        visible=False
    ))
    
    # 设置布局
    fig.update_layout(
        title="动态均值图表测试页面",
        xaxis_title="日期",
        yaxis_title="数值",
        legend_title="图例",
        template='plotly_white',
        height=500,
        hovermode='x unified'
    )
    
    # 动态均值计算的JavaScript代码
    js_code = f'''
    <script>
        // 原始数据
        const data = {df.to_dict('list')};
        const dates = {[str(d) for d in df.index]};
        const seriesNames = {list(df.columns)};
        
        let plotDiv;
        
        document.addEventListener('DOMContentLoaded', function() {{
            plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
            if (!plotDiv) return;
            
            // 监听图例点击
            plotDiv.on('plotly_legendclick', function(eventData) {{
                setTimeout(updateMean, 50);
                return true; // 允许默认行为
            }});
            
            // 监听图例双击
            plotDiv.on('plotly_legenddoubleclick', function(eventData) {{
                setTimeout(updateMean, 50);
                return true;
            }});
            
            function updateMean() {{
                const traces = plotDiv.data;
                const meanTraceIndex = traces.length - 1;
                
                // 找出可见的数据线
                const visibleIndices = [];
                for (let i = 0; i < traces.length - 1; i++) {{
                    const isVisible = traces[i].visible === true || traces[i].visible === undefined;
                    console.log('线条', i, '(', seriesNames[i], ') 可见状态:', traces[i].visible, '是否可见:', isVisible);
                    if (isVisible) {{
                        visibleIndices.push(i);
                    }}
                }}

                console.log('当前可见线条数量:', visibleIndices.length, '可见索引:', visibleIndices);
                
                if (visibleIndices.length <= 1) {{
                    // 没有可见线或只有一条线，隐藏均值线（单条线的均值没有意义）
                    console.log('🔴 隐藏均值线 - 可见线条数量:', visibleIndices.length);
                    Plotly.restyle(plotDiv, {{'visible': false}}, [meanTraceIndex]);
                    updateStatus('均值线已隐藏（线条数量 ≤ 1）');
                    return;
                }}
                
                // 计算均值
                const meanValues = [];
                for (let i = 0; i < dates.length; i++) {{
                    let sum = 0;
                    let count = 0;
                    
                    for (const idx of visibleIndices) {{
                        sum += traces[idx].y[i];
                        count++;
                    }}
                    
                    meanValues.push(count > 0 ? sum / count : 0);
                }}
                
                // 更新均值线
                const selectedNames = visibleIndices.map(i => seriesNames[i]);
                const meanName = '均值 (' + selectedNames.length + '条线: ' + selectedNames.join(', ') + ')';
                
                console.log('🟢 显示均值线 - 可见线条数量:', visibleIndices.length, '选中:', selectedNames);
                
                Plotly.restyle(plotDiv, {{
                    'y': [meanValues],
                    'visible': true,
                    'name': meanName
                }}, [meanTraceIndex]);
                
                updateStatus('均值线已显示（' + selectedNames.length + '条线）');
            }}
            
            // 初始化
            updateMean();
            
            // 暴露函数给全局使用
            window.updateMean = updateMean;
            window.plotDiv = plotDiv;
        }});
        
        // 更新状态显示
        function updateStatus(message) {{
            const statusDiv = document.getElementById('status');
            if (statusDiv) {{
                statusDiv.innerHTML = '<strong>状态:</strong> ' + message + ' <em>(' + new Date().toLocaleTimeString() + ')</em>';
            }}
        }}
        
        // 测试函数
        function testShowOnlyLineA() {{
            console.log('🧪 测试: 只显示Line A');
            Plotly.restyle(plotDiv, {{'visible': 'legendonly'}}, [1, 2]); // 隐藏Line B和C
            Plotly.restyle(plotDiv, {{'visible': true}}, [0]); // 显示Line A
            setTimeout(updateMean, 100);
        }}
        
        function testShowTwoLines() {{
            console.log('🧪 测试: 显示Line A和B');
            Plotly.restyle(plotDiv, {{'visible': true}}, [0, 1]); // 显示Line A和B
            Plotly.restyle(plotDiv, {{'visible': 'legendonly'}}, [2]); // 隐藏Line C
            setTimeout(updateMean, 100);
        }}
        
        function testShowAllLines() {{
            console.log('🧪 测试: 显示所有线');
            Plotly.restyle(plotDiv, {{'visible': true}}, [0, 1, 2]); // 显示所有线
            setTimeout(updateMean, 100);
        }}
        
        function testHideAllLines() {{
            console.log('🧪 测试: 隐藏所有线');
            Plotly.restyle(plotDiv, {{'visible': 'legendonly'}}, [0, 1, 2]); // 隐藏所有线
            setTimeout(updateMean, 100);
        }}
        
        // 暴露测试函数
        window.testShowOnlyLineA = testShowOnlyLineA;
        window.testShowTwoLines = testShowTwoLines;
        window.testShowAllLines = testShowAllLines;
        window.testHideAllLines = testHideAllLines;
    </script>
    '''
    
    # 生成HTML
    html = fig.to_html(include_plotlyjs='cdn', full_html=False)
    
    full_html = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>动态均值图表测试页面</title>
        <style>
            body {{ 
                font-family: Arial, sans-serif; 
                margin: 20px; 
                background-color: #f5f5f5;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .info {{ 
                background: #e3f2fd; 
                padding: 15px; 
                border-radius: 5px; 
                margin-bottom: 20px;
                border-left: 4px solid #2196f3;
            }}
            .test-controls {{
                background: #f3e5f5;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                border-left: 4px solid #9c27b0;
            }}
            .test-controls h3 {{
                margin-top: 0;
                color: #7b1fa2;
            }}
            .btn {{
                background-color: #2196f3;
                color: white;
                border: none;
                padding: 8px 16px;
                margin: 5px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            }}
            .btn:hover {{
                background-color: #1976d2;
            }}
            .btn.danger {{
                background-color: #f44336;
            }}
            .btn.danger:hover {{
                background-color: #d32f2f;
            }}
            .btn.success {{
                background-color: #4caf50;
            }}
            .btn.success:hover {{
                background-color: #388e3c;
            }}
            #status {{
                background: #fff3e0;
                padding: 10px;
                border-radius: 4px;
                margin-top: 10px;
                border-left: 4px solid #ff9800;
                font-family: monospace;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="info">
                <h3>🔧 Bug修复验证</h3>
                <p><strong>修复内容:</strong> 当只有一条线可见时，均值线应该自动隐藏（因为单条线的"均值"没有意义）</p>
                <ul>
                    <li><strong>手动测试:</strong> 双击任意图例项，观察均值线是否隐藏</li>
                    <li><strong>自动测试:</strong> 使用下方按钮进行各种场景测试</li>
                    <li><strong>控制台日志:</strong> 按F12打开开发者工具查看详细日志</li>
                </ul>
            </div>
            
            <div class="test-controls">
                <h3>🧪 自动化测试控制</h3>
                <button class="btn danger" onclick="testShowOnlyLineA()">只显示Line A (应隐藏均值线)</button>
                <button class="btn success" onclick="testShowTwoLines()">显示Line A+B (应显示均值线)</button>
                <button class="btn success" onclick="testShowAllLines()">显示所有线 (应显示均值线)</button>
                <button class="btn danger" onclick="testHideAllLines()">隐藏所有线 (应隐藏均值线)</button>
                
                <div id="status">状态: 等待测试...</div>
            </div>
            
            {html}
            {js_code}
        </div>
    </body>
    </html>
    '''
    
    return full_html

if __name__ == "__main__":
    html_content = create_test_page()
    
    with open('test_dynamic_mean_fix.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("测试页面已生成: test_dynamic_mean_fix.html")
    print("\n🔍 测试说明:")
    print("1. 打开页面后，观察初始状态（3条线，均值线显示）")
    print("2. 双击任意图例项，只显示一条线，均值线应该隐藏")
    print("3. 单击其他图例项，显示多条线，均值线应该重新显示")
    print("4. 使用页面上的测试按钮进行自动化测试")
    print("5. 按F12打开控制台查看详细日志")
