"""
简化版动态均值图表示例
专门针对选中legend求均值并动态更新的需求
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from datetime import datetime, timed<PERSON><PERSON>

def create_simple_dynamic_chart():
    """创建简单的动态均值图表"""
    
    # 创建示例数据
    dates = pd.date_range('2024-01-01', periods=50, freq='D')
    np.random.seed(42)
    
    # 生成5条不同的时间序列
    data = {
        'Series A': np.cumsum(np.random.randn(50) * 0.1),
        'Series B': np.cumsum(np.random.randn(50) * 0.15),
        'Series C': np.cumsum(np.random.randn(50) * 0.12),
        'Series D': np.cumsum(np.random.randn(50) * 0.08),
        'Series E': np.cumsum(np.random.randn(50) * 0.18)
    }
    
    df = pd.DataFrame(data, index=dates)
    
    # 创建图表
    fig = go.Figure()
    
    # 添加数据线
    colors = ['blue', 'green', 'orange', 'purple', 'brown']
    for i, col in enumerate(df.columns):
        fig.add_trace(go.Scatter(
            x=df.index,
            y=df[col],
            mode='lines',
            name=col,
            line=dict(color=colors[i], width=2),
            opacity=0.8
        ))
    
    # 添加均值线（初始隐藏）
    fig.add_trace(go.Scatter(
        x=df.index,
        y=[0] * len(df),
        mode='lines',
        name='Selected Mean',
        line=dict(color='red', width=3, dash='dash'),
        visible=False
    ))
    
    # 设置布局
    fig.update_layout(
        title="动态均值图表演示",
        xaxis_title="日期",
        yaxis_title="数值",
        legend_title="点击选择/取消选择",
        template='plotly_white',
        height=600,
        hovermode='x unified'
    )
    
    # 动态均值计算的JavaScript代码
    js_code = f'''
    <script>
        // 原始数据
        const data = {df.to_dict('list')};
        const dates = {[str(d) for d in df.index]};
        const seriesNames = {list(df.columns)};
        
        document.addEventListener('DOMContentLoaded', function() {{
            const plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
            if (!plotDiv) return;
            
            // 监听图例点击
            plotDiv.on('plotly_legendclick', function(eventData) {{
                setTimeout(updateMean, 50);
                return true; // 允许默认行为
            }});
            
            // 监听图例双击
            plotDiv.on('plotly_legenddoubleclick', function(eventData) {{
                setTimeout(updateMean, 50);
                return true;
            }});
            
            function updateMean() {{
                const traces = plotDiv.data;
                const meanTraceIndex = traces.length - 1;
                
                // 找出可见的数据线
                const visibleIndices = [];
                for (let i = 0; i < traces.length - 1; i++) {{
                    const isVisible = traces[i].visible === true || traces[i].visible === undefined;
                    console.log('线条', i, '(', seriesNames[i], ') 可见状态:', traces[i].visible, '是否可见:', isVisible);
                    if (isVisible) {{
                        visibleIndices.push(i);
                    }}
                }}
                
                console.log('当前可见线条数量:', visibleIndices.length, '可见索引:', visibleIndices);

                if (visibleIndices.length <= 1) {{
                    // 没有可见线或只有一条线，隐藏均值线（单条线的均值没有意义）
                    console.log('🔴 隐藏均值线 - 可见线条数量:', visibleIndices.length);
                    Plotly.restyle(plotDiv, {{'visible': false}}, [meanTraceIndex]);
                    return;
                }}
                
                // 计算均值
                const meanValues = [];
                for (let i = 0; i < dates.length; i++) {{
                    let sum = 0;
                    let count = 0;
                    
                    for (const idx of visibleIndices) {{
                        sum += traces[idx].y[i];
                        count++;
                    }}
                    
                    meanValues.push(count > 0 ? sum / count : 0);
                }}
                
                // 更新均值线
                const selectedNames = visibleIndices.map(i => seriesNames[i]);
                const meanName = '均值 (' + selectedNames.length + '条线: ' + selectedNames.join(', ') + ')';

                console.log('显示均值线 - 可见线条数量:', visibleIndices.length, '选中:', selectedNames);

                Plotly.restyle(plotDiv, {{
                    'y': [meanValues],
                    'visible': true,
                    'name': meanName
                }}, [meanTraceIndex]);
            }}
            
            // 初始化
            updateMean();
        }});
    </script>
    '''
    
    # 生成HTML
    html = fig.to_html(include_plotlyjs='cdn', full_html=False)
    
    full_html = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>动态均值图表</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .info {{ 
                background: #f0f8ff; 
                padding: 15px; 
                border-radius: 5px; 
                margin-bottom: 20px;
                border-left: 4px solid #007acc;
            }}
        </style>
    </head>
    <body>
        <div class="info">
            <h3>使用方法：</h3>
            <ul>
                <li><strong>单击图例</strong>：显示/隐藏对应的数据线</li>
                <li><strong>双击图例</strong>：只显示该线，隐藏其他</li>
                <li><strong>红色虚线</strong>：选中线条的实时均值</li>
            </ul>
        </div>
        {html}
        {js_code}
    </body>
    </html>
    '''
    
    return full_html

if __name__ == "__main__":
    html_content = create_simple_dynamic_chart()
    
    with open('simple_dynamic_mean.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("简化版动态均值图表已生成: simple_dynamic_mean.html")
    print("\n核心功能:")
    print("✓ 点击图例选择/取消选择数据线")
    print("✓ 实时计算选中线条的均值")
    print("✓ 动态更新红色均值线")
    print("✓ 显示选中线条的名称和数量")
