"""
验证可见性检测修复的专用测试脚本
创建一个简单的测试页面来验证线条可见性检测逻辑
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go

def create_visibility_test_page():
    """创建专门测试可见性检测的页面"""
    
    # 创建简单的测试数据
    dates = pd.date_range('2024-01-01', periods=10, freq='D')
    
    data = {
        'Line A': [1, 2, 3, 4, 5, 4, 3, 2, 1, 0],
        'Line B': [0, 1, 2, 3, 4, 5, 4, 3, 2, 1],
        'Line C': [2, 1, 0, 1, 2, 3, 2, 1, 0, 1]
    }
    
    df = pd.DataFrame(data, index=dates)
    
    # 创建图表
    fig = go.Figure()
    
    # 添加数据线
    colors = ['blue', 'green', 'orange']
    for i, col in enumerate(df.columns):
        fig.add_trace(go.Scatter(
            x=df.index,
            y=df[col],
            mode='lines+markers',
            name=col,
            line=dict(color=colors[i], width=3),
            marker=dict(size=8)
        ))
    
    # 添加均值线
    fig.add_trace(go.Scatter(
        x=df.index,
        y=[0] * len(df),
        mode='lines',
        name='Selected Mean',
        line=dict(color='red', width=4, dash='dash'),
        visible=False
    ))
    
    # 设置布局
    fig.update_layout(
        title="可见性检测修复验证",
        xaxis_title="日期",
        yaxis_title="数值",
        legend_title="图例（点击测试）",
        template='plotly_white',
        height=500
    )
    
    # 修复后的JavaScript代码
    js_code = f'''
    <script>
        const data = {df.to_dict('list')};
        const dates = {[str(d) for d in df.index]};
        const seriesNames = {list(df.columns)};
        
        let plotDiv;
        
        document.addEventListener('DOMContentLoaded', function() {{
            plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
            if (!plotDiv) return;
            
            // 监听图例点击
            plotDiv.on('plotly_legendclick', function(eventData) {{
                console.log('=== 图例点击事件 ===');
                setTimeout(updateMean, 100);
                return true;
            }});
            
            // 监听图例双击
            plotDiv.on('plotly_legenddoubleclick', function(eventData) {{
                console.log('=== 图例双击事件 ===');
                setTimeout(updateMean, 100);
                return true;
            }});
            
            function updateMean() {{
                console.log('\\n--- 开始更新均值线 ---');
                const traces = plotDiv.data;
                const meanTraceIndex = traces.length - 1;
                
                console.log('总trace数量:', traces.length, '均值线索引:', meanTraceIndex);
                
                // 找出可见的数据线（修复后的逻辑）
                const visibleIndices = [];
                for (let i = 0; i < traces.length - 1; i++) {{
                    const visibleState = traces[i].visible;
                    const isVisible = visibleState === true || visibleState === undefined;
                    
                    console.log(`线条 ${{i}} (${{seriesNames[i]}}):`, {{
                        'visible属性': visibleState,
                        '是否可见': isVisible,
                        '类型': typeof visibleState
                    }});
                    
                    if (isVisible) {{
                        visibleIndices.push(i);
                    }}
                }}
                
                console.log('\\n可见线条统计:');
                console.log('- 可见索引:', visibleIndices);
                console.log('- 可见数量:', visibleIndices.length);
                console.log('- 可见名称:', visibleIndices.map(i => seriesNames[i]));
                
                // 更新状态显示
                updateTestStatus(visibleIndices);
                
                if (visibleIndices.length <= 1) {{
                    console.log('\\n🔴 隐藏均值线 - 原因: 可见线条数量 ≤ 1');
                    Plotly.restyle(plotDiv, {{'visible': false}}, [meanTraceIndex]);
                    return;
                }}
                
                // 计算均值
                const meanValues = [];
                for (let i = 0; i < dates.length; i++) {{
                    let sum = 0;
                    let count = 0;
                    
                    for (const idx of visibleIndices) {{
                        sum += traces[idx].y[i];
                        count++;
                    }}
                    
                    meanValues.push(count > 0 ? sum / count : 0);
                }}
                
                // 更新均值线
                const selectedNames = visibleIndices.map(i => seriesNames[i]);
                const meanName = `均值 (${{selectedNames.length}}条线: ${{selectedNames.join(', ')}})`;
                
                console.log('\\n🟢 显示均值线:');
                console.log('- 名称:', meanName);
                console.log('- 选中线条:', selectedNames);
                console.log('- 均值样本:', meanValues.slice(0, 3), '...');
                
                Plotly.restyle(plotDiv, {{
                    'y': [meanValues],
                    'visible': true,
                    'name': meanName
                }}, [meanTraceIndex]);
                
                console.log('--- 更新完成 ---\\n');
            }}
            
            // 初始化
            updateMean();
            
            // 暴露函数
            window.updateMean = updateMean;
            window.plotDiv = plotDiv;
        }});
        
        function updateTestStatus(visibleIndices) {{
            const statusDiv = document.getElementById('test-status');
            if (!statusDiv) return;
            
            const visibleNames = visibleIndices.map(i => seriesNames[i]);
            const timestamp = new Date().toLocaleTimeString();
            
            statusDiv.innerHTML = `
                <strong>当前状态 (${{timestamp}}):</strong><br>
                可见线条数量: <span class="count">${{visibleIndices.length}}</span><br>
                可见线条: <span class="names">${{visibleNames.join(', ') || '无'}}</span><br>
                均值线状态: <span class="mean-status">${{visibleIndices.length > 1 ? '显示' : '隐藏'}}</span>
            `;
        }}
        
        // 测试函数
        function testHideLineC() {{
            console.log('\\n🧪 测试: 隐藏Line C');
            Plotly.restyle(plotDiv, {{'visible': 'legendonly'}}, [2]);
            setTimeout(updateMean, 100);
        }}
        
        function testShowOnlyLineA() {{
            console.log('\\n🧪 测试: 只显示Line A');
            Plotly.restyle(plotDiv, {{'visible': 'legendonly'}}, [1, 2]);
            Plotly.restyle(plotDiv, {{'visible': true}}, [0]);
            setTimeout(updateMean, 100);
        }}
        
        function testShowAll() {{
            console.log('\\n🧪 测试: 显示所有线');
            Plotly.restyle(plotDiv, {{'visible': true}}, [0, 1, 2]);
            setTimeout(updateMean, 100);
        }}
        
        window.testHideLineC = testHideLineC;
        window.testShowOnlyLineA = testShowOnlyLineA;
        window.testShowAll = testShowAll;
    </script>
    '''
    
    # 生成HTML
    html = fig.to_html(include_plotlyjs='cdn', full_html=False)
    
    full_html = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>可见性检测修复验证</title>
        <style>
            body {{ 
                font-family: Arial, sans-serif; 
                margin: 20px; 
                background-color: #f0f8ff;
            }}
            .container {{
                max-width: 1000px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }}
            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 20px;
                text-align: center;
            }}
            .test-panel {{
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                border-left: 4px solid #007bff;
            }}
            .btn {{
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                margin: 5px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                transition: background-color 0.3s;
            }}
            .btn:hover {{ background-color: #0056b3; }}
            .btn.danger {{ background-color: #dc3545; }}
            .btn.danger:hover {{ background-color: #c82333; }}
            .btn.success {{ background-color: #28a745; }}
            .btn.success:hover {{ background-color: #218838; }}
            
            #test-status {{
                background: #e9ecef;
                padding: 15px;
                border-radius: 8px;
                margin-top: 15px;
                font-family: monospace;
                border-left: 4px solid #6c757d;
            }}
            .count {{ 
                font-weight: bold; 
                color: #007bff; 
                font-size: 18px;
            }}
            .names {{ 
                font-weight: bold; 
                color: #28a745; 
            }}
            .mean-status {{ 
                font-weight: bold; 
                color: #dc3545; 
            }}
            .instructions {{
                background: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                border-left: 4px solid #ffc107;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔧 可见性检测修复验证</h1>
                <p>验证动态均值图表中线条可见性检测逻辑的修复效果</p>
            </div>
            
            <div class="instructions">
                <h3>📋 测试说明</h3>
                <ul>
                    <li><strong>问题描述:</strong> 隐藏线条后，均值线名称中的线条数量仍然不正确</li>
                    <li><strong>修复内容:</strong> 改进可见性检测逻辑，正确识别 visible 状态</li>
                    <li><strong>验证方法:</strong> 点击图例或使用测试按钮，观察控制台日志和状态显示</li>
                    <li><strong>预期结果:</strong> 均值线名称中的线条数量应与实际可见线条数量一致</li>
                </ul>
            </div>
            
            <div class="test-panel">
                <h3>🧪 自动化测试</h3>
                <button class="btn danger" onclick="testHideLineC()">隐藏Line C (应显示2条线的均值)</button>
                <button class="btn danger" onclick="testShowOnlyLineA()">只显示Line A (应隐藏均值线)</button>
                <button class="btn success" onclick="testShowAll()">显示所有线 (应显示3条线的均值)</button>
                
                <div id="test-status">等待测试...</div>
            </div>
            
            {html}
            {js_code}
        </div>
    </body>
    </html>
    '''
    
    return full_html

if __name__ == "__main__":
    html_content = create_visibility_test_page()
    
    with open('verify_visibility_fix.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("可见性检测修复验证页面已生成: verify_visibility_fix.html")
    print("\n🔍 验证重点:")
    print("1. 检查控制台日志中每条线的 visible 状态")
    print("2. 验证可见线条数量统计是否正确")
    print("3. 确认均值线名称中的数量与实际一致")
    print("4. 测试各种图例操作场景")
    print("\n💡 使用方法:")
    print("- 按F12打开开发者工具查看详细日志")
    print("- 手动点击图例测试")
    print("- 使用页面上的测试按钮进行自动化测试")
