
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>动态均值图表测试页面</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 20px; 
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .info { 
                background: #e3f2fd; 
                padding: 15px; 
                border-radius: 5px; 
                margin-bottom: 20px;
                border-left: 4px solid #2196f3;
            }
            .test-controls {
                background: #f3e5f5;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                border-left: 4px solid #9c27b0;
            }
            .test-controls h3 {
                margin-top: 0;
                color: #7b1fa2;
            }
            .btn {
                background-color: #2196f3;
                color: white;
                border: none;
                padding: 8px 16px;
                margin: 5px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            }
            .btn:hover {
                background-color: #1976d2;
            }
            .btn.danger {
                background-color: #f44336;
            }
            .btn.danger:hover {
                background-color: #d32f2f;
            }
            .btn.success {
                background-color: #4caf50;
            }
            .btn.success:hover {
                background-color: #388e3c;
            }
            #status {
                background: #fff3e0;
                padding: 10px;
                border-radius: 4px;
                margin-top: 10px;
                border-left: 4px solid #ff9800;
                font-family: monospace;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="info">
                <h3>🔧 Bug修复验证</h3>
                <p><strong>修复内容:</strong> 当只有一条线可见时，均值线应该自动隐藏（因为单条线的"均值"没有意义）</p>
                <ul>
                    <li><strong>手动测试:</strong> 双击任意图例项，观察均值线是否隐藏</li>
                    <li><strong>自动测试:</strong> 使用下方按钮进行各种场景测试</li>
                    <li><strong>控制台日志:</strong> 按F12打开开发者工具查看详细日志</li>
                </ul>
            </div>
            
            <div class="test-controls">
                <h3>🧪 自动化测试控制</h3>
                <button class="btn danger" onclick="testShowOnlyLineA()">只显示Line A (应隐藏均值线)</button>
                <button class="btn success" onclick="testShowTwoLines()">显示Line A+B (应显示均值线)</button>
                <button class="btn success" onclick="testShowAllLines()">显示所有线 (应显示均值线)</button>
                <button class="btn danger" onclick="testHideAllLines()">隐藏所有线 (应隐藏均值线)</button>
                
                <div id="status">状态: 等待测试...</div>
            </div>
            
            <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-2.35.2.min.js"></script>                <div id="418e2d76-072c-4b08-8f82-92d9d0440488" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("418e2d76-072c-4b08-8f82-92d9d0440488")) {                    Plotly.newPlot(                        "418e2d76-072c-4b08-8f82-92d9d0440488",                        [{"line":{"color":"blue","width":2},"mode":"lines","name":"Line A","opacity":0.8,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00"],"y":[0.04967141530112327,0.0358449851840048,0.10061383899407406,0.25291682463487664,0.22950148716254304,0.206087791467625,0.36400907301836416,0.44075254593365504,0.39380510734015983,0.4480611116987563,0.40171934241751006,0.35514636706048436,0.37934259421708777,0.18801456975130798,0.015522786500004687,-0.040705966424092584,-0.14198907845753497,-0.11056434519800759,-0.2013667527501287,-0.34259712288365785,-0.19603224599150243,-0.218609876040156,-0.2118570555713636,-0.3543318741927093,-0.40877014664522754,-0.39767788767424095,-0.5127772454164712,-0.475207443581904,-0.5352713125737845,-0.5644406875531122],"type":"scatter"},{"line":{"color":"green","width":2},"mode":"lines","name":"Line B","opacity":0.8,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00"],"y":[-0.09025599183440954,0.1875857358419311,0.18556115213124103,0.026904512787855966,0.1502862496033343,-0.032840297892319015,-0.001510758641605707,-0.295461277223572,-0.49468918455833655,-0.465159999177968,-0.35439001217865645,-0.3286847700001609,-0.346032012358397,-0.3911975666967903,-0.6129758652519044,-0.7209524965111107,-0.7900483121550789,-0.6314799782222416,-0.5799372347869723,-0.8443932580913824,-0.7957806626821632,-0.8535430047446106,-0.9550813047905045,-0.8633298614643743,-0.7086799330899817,-0.5689879152225519,-0.6948705437059477,-0.7412524000836299,-0.6915628853730953,-0.5452311163047414],"type":"scatter"},{"line":{"color":"orange","width":2},"mode":"lines","name":"Line C","opacity":0.8,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00"],"y":[-0.05750090854143479,-0.07977998574109284,-0.21254018262181623,-0.3560849775114967,-0.2585818788241929,-0.09583307539569419,-0.10447428998533426,0.015949657761708633,0.05934598076742473,-0.01806838978519018,0.025299082875819487,0.2098634708517358,0.2055643461585416,0.3933215848562224,0.07895217236545304,0.1775804728904799,0.18802612107906044,0.15214523902315635,0.16315653220741663,-0.07535173754469049,-0.10171236408519192,-0.05885885550378236,0.11848842986519956,0.05629600367236187,-0.040723228674820644,-0.10093407390496503,0.008914180219283863,0.048364313378445996,-0.015206911073598665,0.04638518090000406],"type":"scatter"},{"line":{"color":"red","dash":"dash","width":3},"mode":"lines","name":"Selected Mean","visible":false,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00"],"y":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],"type":"scatter"}],                        {"template":{"data":{"barpolar":[{"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"white","showlakes":true,"showland":true,"subunitcolor":"#C8D4E3"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"white","polar":{"angularaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""},"bgcolor":"white","radialaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"yaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"zaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"baxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"bgcolor":"white","caxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2}}},"title":{"text":"动态均值图表测试页面"},"xaxis":{"title":{"text":"日期"}},"yaxis":{"title":{"text":"数值"}},"legend":{"title":{"text":"图例"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };                            </script>        </div>
            
    <script>
        // 原始数据
        const data = {'Line A': [0.04967141530112327, 0.0358449851840048, 0.10061383899407406, 0.25291682463487664, 0.22950148716254304, 0.206087791467625, 0.36400907301836416, 0.44075254593365504, 0.39380510734015983, 0.4480611116987563, 0.40171934241751006, 0.35514636706048436, 0.37934259421708777, 0.18801456975130798, 0.015522786500004687, -0.040705966424092584, -0.14198907845753497, -0.11056434519800759, -0.2013667527501287, -0.34259712288365785, -0.19603224599150243, -0.218609876040156, -0.2118570555713636, -0.3543318741927093, -0.40877014664522754, -0.39767788767424095, -0.5127772454164712, -0.475207443581904, -0.5352713125737845, -0.5644406875531122], 'Line B': [-0.09025599183440954, 0.1875857358419311, 0.18556115213124103, 0.026904512787855966, 0.1502862496033343, -0.032840297892319015, -0.001510758641605707, -0.295461277223572, -0.49468918455833655, -0.465159999177968, -0.35439001217865645, -0.3286847700001609, -0.346032012358397, -0.3911975666967903, -0.6129758652519044, -0.7209524965111107, -0.7900483121550789, -0.6314799782222416, -0.5799372347869723, -0.8443932580913824, -0.7957806626821632, -0.8535430047446106, -0.9550813047905045, -0.8633298614643743, -0.7086799330899817, -0.5689879152225519, -0.6948705437059477, -0.7412524000836299, -0.6915628853730953, -0.5452311163047414], 'Line C': [-0.05750090854143479, -0.07977998574109284, -0.21254018262181623, -0.3560849775114967, -0.2585818788241929, -0.09583307539569419, -0.10447428998533426, 0.015949657761708633, 0.05934598076742473, -0.01806838978519018, 0.025299082875819487, 0.2098634708517358, 0.2055643461585416, 0.3933215848562224, 0.07895217236545304, 0.1775804728904799, 0.18802612107906044, 0.15214523902315635, 0.16315653220741663, -0.07535173754469049, -0.10171236408519192, -0.05885885550378236, 0.11848842986519956, 0.05629600367236187, -0.040723228674820644, -0.10093407390496503, 0.008914180219283863, 0.048364313378445996, -0.015206911073598665, 0.04638518090000406]};
        const dates = ['2024-01-01 00:00:00', '2024-01-02 00:00:00', '2024-01-03 00:00:00', '2024-01-04 00:00:00', '2024-01-05 00:00:00', '2024-01-06 00:00:00', '2024-01-07 00:00:00', '2024-01-08 00:00:00', '2024-01-09 00:00:00', '2024-01-10 00:00:00', '2024-01-11 00:00:00', '2024-01-12 00:00:00', '2024-01-13 00:00:00', '2024-01-14 00:00:00', '2024-01-15 00:00:00', '2024-01-16 00:00:00', '2024-01-17 00:00:00', '2024-01-18 00:00:00', '2024-01-19 00:00:00', '2024-01-20 00:00:00', '2024-01-21 00:00:00', '2024-01-22 00:00:00', '2024-01-23 00:00:00', '2024-01-24 00:00:00', '2024-01-25 00:00:00', '2024-01-26 00:00:00', '2024-01-27 00:00:00', '2024-01-28 00:00:00', '2024-01-29 00:00:00', '2024-01-30 00:00:00'];
        const seriesNames = ['Line A', 'Line B', 'Line C'];
        
        let plotDiv;
        
        document.addEventListener('DOMContentLoaded', function() {
            plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
            if (!plotDiv) return;
            
            // 监听图例点击
            plotDiv.on('plotly_legendclick', function(eventData) {
                setTimeout(updateMean, 50);
                return true; // 允许默认行为
            });
            
            // 监听图例双击
            plotDiv.on('plotly_legenddoubleclick', function(eventData) {
                setTimeout(updateMean, 50);
                return true;
            });
            
            function updateMean() {
                const traces = plotDiv.data;
                const meanTraceIndex = traces.length - 1;
                
                // 找出可见的数据线
                const visibleIndices = [];
                for (let i = 0; i < traces.length - 1; i++) {
                    const isVisible = traces[i].visible === true || traces[i].visible === undefined;
                    console.log('线条', i, '(', seriesNames[i], ') 可见状态:', traces[i].visible, '是否可见:', isVisible);
                    if (isVisible) {
                        visibleIndices.push(i);
                    }
                }

                console.log('当前可见线条数量:', visibleIndices.length, '可见索引:', visibleIndices);
                
                if (visibleIndices.length <= 1) {
                    // 没有可见线或只有一条线，隐藏均值线（单条线的均值没有意义）
                    console.log('🔴 隐藏均值线 - 可见线条数量:', visibleIndices.length);
                    Plotly.restyle(plotDiv, {'visible': false}, [meanTraceIndex]);
                    updateStatus('均值线已隐藏（线条数量 ≤ 1）');
                    return;
                }
                
                // 计算均值
                const meanValues = [];
                for (let i = 0; i < dates.length; i++) {
                    let sum = 0;
                    let count = 0;
                    
                    for (const idx of visibleIndices) {
                        sum += traces[idx].y[i];
                        count++;
                    }
                    
                    meanValues.push(count > 0 ? sum / count : 0);
                }
                
                // 更新均值线
                const selectedNames = visibleIndices.map(i => seriesNames[i]);
                const meanName = '均值 (' + selectedNames.length + '条线: ' + selectedNames.join(', ') + ')';
                
                console.log('🟢 显示均值线 - 可见线条数量:', visibleIndices.length, '选中:', selectedNames);
                
                Plotly.restyle(plotDiv, {
                    'y': [meanValues],
                    'visible': true,
                    'name': meanName
                }, [meanTraceIndex]);
                
                updateStatus('均值线已显示（' + selectedNames.length + '条线）');
            }
            
            // 初始化
            updateMean();
            
            // 暴露函数给全局使用
            window.updateMean = updateMean;
            window.plotDiv = plotDiv;
        });
        
        // 更新状态显示
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            if (statusDiv) {
                statusDiv.innerHTML = '<strong>状态:</strong> ' + message + ' <em>(' + new Date().toLocaleTimeString() + ')</em>';
            }
        }
        
        // 测试函数
        function testShowOnlyLineA() {
            console.log('🧪 测试: 只显示Line A');
            Plotly.restyle(plotDiv, {'visible': 'legendonly'}, [1, 2]); // 隐藏Line B和C
            Plotly.restyle(plotDiv, {'visible': true}, [0]); // 显示Line A
            setTimeout(updateMean, 100);
        }
        
        function testShowTwoLines() {
            console.log('🧪 测试: 显示Line A和B');
            Plotly.restyle(plotDiv, {'visible': true}, [0, 1]); // 显示Line A和B
            Plotly.restyle(plotDiv, {'visible': 'legendonly'}, [2]); // 隐藏Line C
            setTimeout(updateMean, 100);
        }
        
        function testShowAllLines() {
            console.log('🧪 测试: 显示所有线');
            Plotly.restyle(plotDiv, {'visible': true}, [0, 1, 2]); // 显示所有线
            setTimeout(updateMean, 100);
        }
        
        function testHideAllLines() {
            console.log('🧪 测试: 隐藏所有线');
            Plotly.restyle(plotDiv, {'visible': 'legendonly'}, [0, 1, 2]); // 隐藏所有线
            setTimeout(updateMean, 100);
        }
        
        // 暴露测试函数
        window.testShowOnlyLineA = testShowOnlyLineA;
        window.testShowTwoLines = testShowTwoLines;
        window.testShowAllLines = testShowAllLines;
        window.testHideAllLines = testHideAllLines;
    </script>
    
        </div>
    </body>
    </html>
    